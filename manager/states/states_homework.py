from common.manager_tests.get_transitions_handlers import get_transitions_handlers
from manager.handlers.homework import (
    start_add_homework, select_subject, select_lesson, save_homework,
    select_homework_to_delete, show_homeworks_to_delete, show_homework_management
)
from manager.handlers.homework import AddHomeworkStates
transitions, handlers = get_transitions_handlers(AddHomeworkStates, "manager")
# Словарь переходов между состояниями
STATE_TRANSITIONS = {
    AddHomeworkStates.main: None,  # Возврат в главное меню менеджера
    AddHomeworkStates.select_course: AddHomeworkStates.main,  # Возврат к выбору действия (добавить/удалить)
    AddHomeworkStates.select_subject: AddHomeworkStates.select_course,
    AddHomeworkStates.select_lesson: AddHomeworkStates.select_subject,
    AddHomeworkStates.choose_creation_method: AddHomeworkStates.select_lesson,  # Возврат к выбору урока
    AddHomeworkStates.delete_test: AddHomeworkStates.main,  # Возврат к выбору действия
    AddHomeworkStates.select_test_to_delete: AddHomeworkStates.delete_test,
    **transitions
}

# Словарь обработчиков для каждого состояния
STATE_HANDLERS = {
    AddHomeworkStates.main: show_homework_management,  # Показ меню управления ДЗ
    AddHomeworkStates.select_course: start_add_homework,
    AddHomeworkStates.select_subject: select_subject,
    AddHomeworkStates.select_lesson: select_lesson,
    AddHomeworkStates.delete_test: select_homework_to_delete,
    AddHomeworkStates.select_test_to_delete: show_homeworks_to_delete,
    **handlers
}